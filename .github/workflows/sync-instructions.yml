name: <PERSON><PERSON><PERSON> bộ global-instructions.md sang các file hướng dẫn

on:
  pull_request:
    paths:
      - 'global-instructions.md'

jobs:
  sync-instructions:
    runs-on: ubuntu-latest
    steps:
      - name: <PERSON><PERSON><PERSON> tra mã nguồ<PERSON>
        uses: actions/checkout@v4

      - name: <PERSON><PERSON> chép global-instructions.md sang AGENTS.md
        run: cp global-instructions.md AGENTS.md

      - name: Sao chép global-instructions.md sang CLAUDE.md
        run: cp global-instructions.md CLAUDE.md

      - name: Sao chép global-instructions.md sang GEMINI.md
        run: cp global-instructions.md GEMINI.md

      - name: Sao chép global-instructions.md sang copilot-instructions.md
        run: cp global-instructions.md .github/copilot-instructions.md

      - name: Sao chép global-instructions.md sang augment-instructions.md
        run: cp global-instructions.md .augment/rules/imported/augment-instructions.md

      - name: <PERSON><PERSON>u hình git user
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Thêm các file hướng dẫn
        run: git add *

      - name: Commit thay đổi
        run: git commit -m "Đồng bộ các file hướng dẫn với global-instructions.md" || echo "No changes to commit"

      - name: Đẩy thay đổi lên remote
        run: git push origin HEAD:${{ github.head_ref }}
